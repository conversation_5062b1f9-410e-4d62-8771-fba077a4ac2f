// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.GTW;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Managers;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Isto.Core.Localization;
using Isto.GTW.Providers;
using UnityEngine;
using Zenject;

/// <summary>
/// Be warned that this class is very much a work in progress and pretty dirty.
/// The core speedrun feature is actively being worked on.
/// </summary>
public class GTWProgressProvider : GameProgressProvider, IDataLoadCompleteHandler
{
    // OTHER FIELDS

    private CheckpointData _checkpointData;
    private CheckpointData.LevelCheckpointData _bestCheckpoint;
    private int _bestCheckpointIndex = 0; // index in the timers, not index in the checkpoints list
    private CheckpointData.LevelCheckpointData[] _indexableLevelsCache = null;
    private Dictionary<int, CheckpointData.LevelCheckpointData> _filteredLevelsCache = null;
    private bool _init = false;


    // PROPERTIES

    /// <summary>
    /// Indexed using speedrun timer indices, not our GTW checkpoint Ids which are all over the place.
    /// </summary>
    public Dictionary<int, float> GameTime { get; set; }

    public bool GamePaused { get; private set; } = false;
    public bool GameEnded { get; private set; } = false;

    // There's a lot of checkpoints in GTW so we don't want to put them all in the speedrun UI.
    // We've handpicked those we want to use with a showInSpeedrunTimer boolean.
    // This filtered list allows us to know the set we're working with, and to translate checkpoint ID into
    // "timer list" ID (which only knows about the included checkpoints).
    private Dictionary<int, CheckpointData.LevelCheckpointData> FilteredLevels
    {
        get
        {
            if (_filteredLevelsCache == null)
            {
                _filteredLevelsCache = new Dictionary<int, CheckpointData.LevelCheckpointData>();

                if (_checkpointData != null && _checkpointData.levelCheckpointDatas != null)
                {
                    for (int i = 0; i < _checkpointData.levelCheckpointDatas.Count; i++)
                    {
                        CheckpointData.LevelCheckpointData current = _checkpointData.levelCheckpointDatas[i];
                        if (current.showInSpeedrunTimer || _gameState.IsDoinklerSpecial)
                        {
                            _filteredLevelsCache.Add(i, current);
                        }
                    }
                }
            }

            return _filteredLevelsCache;
        }
    }

    private CheckpointData.LevelCheckpointData[] IndexableLevels
    {
        get
        {
            if (_indexableLevelsCache == null)
            {
                _indexableLevelsCache = FilteredLevels.Values.ToArray();
            }
            return _indexableLevelsCache;
        }
    }


    // INJECTION

    private CheckpointManager _checkpointManager;
    private GTWGameState _gameState;
    private TimeManager _timeManager;
    private LocTerm.Factory _localizedStringFactory;
    private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;


    // Scope mismatch - fix later
    [Inject]
    private void Inject(/*CheckpointManager checkpointManager,*/ GTWGameState gameState, TimeManager timeManager, LocTerm.Factory localizedStringFactory, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider)
    {
        //_checkpointManager = checkpointManager;
        _gameState = gameState;
        _timeManager = timeManager;
        _localizedStringFactory = localizedStringFactory;
        _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
    }

    private void Awake()
    {
        if (GameTime == null)
        {
            GameTime = new Dictionary<int, float>();
        }
        GamePaused = true;
        RegisterEvents();
    }

    private IEnumerator Start()
    {
        while (!_init)
        {
            yield return null;
        }

        _checkpointData = _checkpointManager.CheckpointData;
        _bestCheckpoint = _checkpointData.StartingCheckpoint;

        Debug.Assert(_checkpointData != null);
        Debug.Assert(_bestCheckpoint != null);

#if UNITY_GAMECORE
        // Since Xbox loading is async, we need to wait for it to finish loading before we can
        // initialize the starting checkpoint index
        if (GameState.LoadingFromSave)
        {
            while (GameState.CurrentlyLoading)
            {
                yield return null;
            }
        }
#endif

        InitStartingCheckpointIndex();
    }

    private void OnDestroy()
    {
        UnregisterEvents();
    }

    private void Update()
    {
        if (!_init)
        {
            // Bootleg injection so the provider works for now.
            CheckpointManager cpm = GameObject.FindAnyObjectByType<CheckpointManager>();
            if (cpm != null)
            {
                _checkpointManager = cpm;
                _init = true;
            }
            else
            {
                return;
            }
        }

        if (_checkpointManager.ActiveCheckpoint == null)
            return;

        UpdateCheckpoint();

        if (GamePaused)
        {
            return;
        }

        TrackTime();
    }


    // EVENTS

    private void RegisterEvents()
    {
        Events.Subscribe(GTWEvents.PLAYER_FIRST_INPUT, Events_OnFirstPlayerInput);
        Events.Subscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnDoinklerStageComplete);
        Events.Subscribe(GTWEvents.DOINKLER_STAGE_RESET, Events_OnDoinklerStageReset);
        Events.Subscribe(Events.GAME_END, Events_OnGameEnd);
    }

    private void UnregisterEvents()
    {
        Events.UnSubscribe(GTWEvents.PLAYER_FIRST_INPUT, Events_OnFirstPlayerInput);
        Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnDoinklerStageComplete);
        Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_RESET, Events_OnDoinklerStageReset);
        Events.UnSubscribe(Events.GAME_END, Events_OnGameEnd);
    }

    private void Events_OnDoinklerStageComplete()
    {
        GamePaused = true;
    }

    private void Events_OnDoinklerStageReset()
    {
        if (GameEnded)
        {
            GameEnded = false;
        }
    }

    private void Events_OnFirstPlayerInput()
    {
        GamePaused = false;
    }

    private void Events_OnGameEnd()
    {
        _bestCheckpointIndex = FilteredLevels.Count;
        if (_gameState.IsDoinklerPortfolio)
        {
            _bestCheckpointIndex = _gameState.GetCurrentDoinklerWorld();
        }
        GameEnded = true;
    }

    public void OnDataLoadComplete()
    {
        if (!GameState.LoadingFromSave)
        {
            return;
        }

        // Note: I think at this time the best checkpoint might not be ready yet
        if (!GameTime.ContainsKey(_bestCheckpointIndex))
        {
            GameTime.Add(_bestCheckpointIndex, 0f);
        }

        // This was a trick to support old save data but it would conflict now that split time data is saved
        //GameTime[_bestCheckpointIndex] = _timeManager.TotalGameTime;
    }


    // ACCESSORS

    public override int  GetCurrentGameProgressLevel()
    {
        if (!_init || _checkpointData == null || _checkpointData.levelCheckpointDatas == null)
            return -1;

        return _bestCheckpointIndex;
    }

    public override int GetMaxGameProgressLevel()
    {
        if (!_init || _checkpointData == null || _checkpointData.levelCheckpointDatas == null)
            return -1;

        if (_gameState.IsDoinklerPortfolio)
        {
            return _gameState.GetDoinklerWorldCount();
        }
        /*else if (_gameState.IsDoinklerSpecial)
        {
            return 1; // only 1 level in special chapter count, so completion would be at 1?
        }*/

        return FilteredLevels.Count;
    }

    public override bool IsLastProgressLevel()
    {
        if (!_init || _checkpointData == null || _checkpointData.levelCheckpointDatas == null)
            return false;

        if(_gameState.IsDoinklerPortfolio)
        {
            return GameEnded;
        }

        return base.IsLastProgressLevel();
    }

    public override float GetGameSecondsElapsedInLevel(int progressLevel)
    {
        return GetCumulativeGameTimeForCheckpoint(progressLevel);
    }

    public override float GetTotalGameSecondsElapsedInPlaythrough()
    {
        return GameTime.Sum(x => x.Value);
    }


    public override string GetGameProgressLevelInternalName(int progressLevel)
    {
        return IndexableLevels[progressLevel].checkpointName;
    }

    private float GetCumulativeGameTimeForCheckpoint(int checkpointId)
    {
        //return GameTime.Where(x => x.Key <= checkpointId).Sum(y => y.Value);


        if (GameTime.ContainsKey(checkpointId))
            return GameTime[checkpointId];
        return
            0f;
    }


    // OTHER METHODS

    private void InitStartingCheckpointIndex()
    {
        int startingCheckpointId = -1;

        for (int i = 0; i < IndexableLevels.Length; i++)
        {
            float time = GetCumulativeGameTimeForCheckpoint(i);

            if (time > 0.001f)
            {
                startingCheckpointId = i;
            }

            /*if (IndexableLevels[i].checkpointName == _checkpointManager.StartingCheckpoint.checkpointName)
            {
                startingCheckpointId = i;
                break;
            }*/
        }
        /*
        if (_gameState.IsDoinklerSpecial)
        {
            _bestCheckpointIndex = 0; // there should be only one filtered chapter for this mode?
        }
        else*/
        if (_gameState.IsDoinklerPortfolio)
        {
            _bestCheckpointIndex = _gameState.GetCurrentDoinklerWorld();
        }
        else
        {
            _bestCheckpointIndex = startingCheckpointId;
        }
    }

    private void TrackTime()
    {
        // Once player hits the EndOfGame event, time should stop increasing, the game is finished, only cutscene left
        if (GameEnded)
            return;

        // Scaled time so it gets paused when we open the menu. Hopefully that's cool for GTW gameplay
        GameTime[_bestCheckpointIndex] += Time.deltaTime;
    }

    private void UpdateCheckpoint(bool clearCache = false)
    {
        if (_indexableLevelsCache != null && _indexableLevelsCache.Length == 0)
        {
            _indexableLevelsCache = null;
            _filteredLevelsCache = null;
        }

        int activeCheckpointId = -1;

        for (int i = 0; i < IndexableLevels.Length; i++)
        {
            // Be warned that names are only guaranteed unique amongst the chapters that we intend to be seen by the
            // user so if some data gets mixed up in the secondary generic checkpoints and they get added to the list
            // of indexable levels, then the timer will have skips
            if (IndexableLevels[i].checkpointName == _checkpointManager.ActiveCheckpoint.checkpointName)
            {
                activeCheckpointId = i;
                break;
            }
        }

        if (_bestCheckpointIndex == -1 && activeCheckpointId == -1 && IndexableLevels.Length > 0)
        {
            // Waiting for a checkpoint to happen but current checkpoint is not part of the tracked list
            // Since 0 should be tracked this means we've loaded a game and are between 2 listed checkpoints
            // To get out of this pickle we can do a pass on all checkpoints before ActiveCheckpoint
            RestoreProgress();
        }

        if (_bestCheckpoint != null && _bestCheckpointIndex >= activeCheckpointId)
        {
            return;
        }

        _bestCheckpoint = _checkpointManager.ActiveCheckpoint;

        if (_gameState.IsDoinklerPortfolio)
        {
            _bestCheckpointIndex = _gameState.GetCurrentDoinklerWorld();
        }
        /*else if (_gameState.IsDoinklerSpecial)
        {
            _bestCheckpointIndex = 0; // only one "chapter" as far as the timer is concerned
        }*/
        else
        {
            _bestCheckpointIndex = activeCheckpointId;
        }

        if (!GameTime.ContainsKey(_bestCheckpointIndex))
        {
            GameTime.Add(_bestCheckpointIndex, 0f);
        }
    }

    private void RestoreProgress()
    {
        InitStartingCheckpointIndex(); // should set _bestCheckpointIndex to 0 in practice

        if (_bestCheckpointIndex < 0 || _bestCheckpointIndex >= IndexableLevels.Length)
        {
            return;
        }

        _bestCheckpoint = IndexableLevels[_bestCheckpointIndex];

        // Real progress in checkpoint system
        int activeCheckpointIndexInManager = _checkpointData.GetCheckpointId(_checkpointManager.ActiveCheckpoint);

        // Look for those we've passed in the indexables
        for (int i = 0; i < activeCheckpointIndexInManager; i++)
        {
            CheckpointData.LevelCheckpointData cp = _checkpointData.levelCheckpointDatas[i];
            string cpName = cp.checkpointName; // not always unique!
            for (int j = _bestCheckpointIndex; j < IndexableLevels.Length; j++)
            {
                if (IndexableLevels[j].checkpointName == cpName)
                {
                    _bestCheckpointIndex = j;
                    _bestCheckpoint = IndexableLevels[j];
                    break;
                }
            }
        }
    }

    public override void LoadLevelTimes(Dictionary<int, float> dictionary)
    {
        GameTime = new Dictionary<int, float>();
        foreach (KeyValuePair<int, float> levelTime in dictionary)
        {
            GameTime.Add(levelTime.Key, levelTime.Value);
        }

        // If we are in the Doinkler levels, we need to reset the current level time we are on
        if (_gameState.GameLevelDefinition != null)
        {
            GameTime[_gameState.GameLevelDefinition.CheckpointNumber] = 0f;
        }
    }

    public void ResetLevelTimes()
    {
        GameTime.Clear();
        InitStartingCheckpointIndex();
    }
}
// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Game;
using Isto.Core.Localization;
using Isto.Core.Speedrun;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using Isto.GTW.UI;
using System;
using System.Collections;
using TMPro;
using UnityEngine;
using Zenject;

namespace Isto.GTW.Speedrun
{
    public class DoinklerSpeedrunTimer : SpeedrunTimer
    {

        // UNITY HOOKUP

        [Header("Doinkler Hookups")]
        [SerializeField] private TextMeshProUGUI _fileNameHeader;
        [SerializeField] private TextMeshProUGUI _currentLevelTimer;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;


        // OTHER FIELDS

        private LocTerm _currentLevelNameLocalized;

        private bool _isTimeOverride = false;


        // INJECTION

        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;
        private GTWProgressProvider _gtwProgressProvider;
        private GTWGameState _gtwGameState;
        private LocTerm.Factory _localizedStringFactory;


        [Inject]
        public void Inject(GTWGameState gameState, IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider,
            LocTerm.Factory localizedStringFactory)
        {
            _gtwGameState = gameState;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
            _localizedStringFactory = localizedStringFactory;
            _gtwProgressProvider = _gameProgress as GTWProgressProvider;
        }

        protected override void Awake()
        {
            base.Awake();
        }

        protected override IEnumerator Start()
        {
            _fileNameHeader.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _currentLevelTimer.color = _speedrunSettings.GetUninitializedTimerTextColor();
            _chapterName.color = _speedrunSettings.GetUninitializedTimerTextColor();

            yield return base.Start();

            _fileNameHeader.color = _speedrunSettings.GetNormalTimerTextColor();
            _currentLevelTimer.color = _speedrunSettings.GetNormalTimerTextColor();
            _chapterName.color = _speedrunSettings.GetNormalTimerTextColor();
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();
            LocTerm name = _gameProgress.GetGameProgressLevelName(currentLevel);
            name.LocalizeInto(_fileNameHeader);
        }

        protected override void Update()
        {
            base.Update();

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            // For initialization issues
            if (currentLevel == -1)
                return;

            if (_gtwProgressProvider.GameEnded)
            {

                FinishPreviousChapter();

                if (_gameProgress.IsLastProgressLevel())
                {
                    // Finish!
                    _mainTimer.color = SPEEDRUN_GREEN_TEXT;
                    return;
                }
            }

            UpdateTotalWorldTime();
            UpdateCurrentLevelTime();
        }

        private void OnEnable()
        {
            RegisterEvents();
        }

        private void RegisterEvents()
        {
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        private void Events_OnStageStarted()
        {
            _isTimeOverride = false;
            UpdateChapterNameLocalization();
        }

        private void Events_OnStageCompleted()
        {
            _isTimeOverride = true;
        }

        private void OnDisable()
        {
            UnregisterEvents();
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_COMPLETE, Events_OnStageCompleted);
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnStageStarted);
        }

        protected override void UpdateChapterNameLocalization()
        {
            if (!_gtwGameState.GameLevelDefinition)
            {
                // TODO - JP - GTW-747: This occurs when we are in Unity editor and starting from the doinkler level itself and not going through the titlescene flow.
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, "ERROR: No Game Level Definition");
                return;
            }

            if (!string.IsNullOrEmpty(_gtwGameState.GameLevelDefinition.LevelName.mTerm))
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.Localized, _gtwGameState.GameLevelDefinition.LevelName);
            }
            else
            {
                _currentLevelNameLocalized = _localizedStringFactory.Create(LocTerm.LocalizationType.NonLocalized, _gtwGameState.GameLevelDefinition.fallbackLevelName);
            }

        }

        private void UpdateTotalWorldTime()
        {
            if(!_doinklerWorldDefinitionProvider.Current)
                return;

            float totalTime = 0f;
            foreach (GTWGameLevelDefinition gtwGameLevelDefinition in _doinklerWorldDefinitionProvider.Current.GameLevels)
            {
                totalTime += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
            }
            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();


            TimeSpan totalTimeSpan = TimeSpan.FromSeconds(GetIndividualSplit(currentLevel));
            _mainTimer.text = UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(totalTimeSpan);
        }

        private void UpdateCurrentLevelTime()
        {
            if(_isTimeOverride || _currentLevelNameLocalized == null)
                return;

            if(_chapterName.text != _currentLevelNameLocalized.Localize())
            {
                _currentLevelNameLocalized.LocalizeInto(_chapterName);
            }

            int currentLevel = _gameProgress.GetCurrentGameProgressLevel();

            float currentTimeInLevel = _gameProgress.GetGameSecondsElapsedInLevel(currentLevel);

            TimeSpan currentTime = TimeSpan.FromMilliseconds(currentTimeInLevel * 1000f);
            _currentLevelTimer.text = GTWUtils.GetFormattedTimeOrDefaultString(currentTime.TotalSeconds.ToString(), Color.white, false);
        }

        protected override void SetupSegmentTimer(SpeedRunSingleTime timer, int chapter)
        {
            timer.SetColorBG((chapter % 2) == 0 ? bgEven : bgOdd);
            timer.ActivateImage();

            LocTerm name = _gameProgress.GetGameProgressLevelName(chapter);
            timer.SetChapterName(name);

            bool targetTimeExists = IsTargetTimeAvailableForChapter(chapter);
            if (_displaySplitsTargets && targetTimeExists)
            {
                TimeSpan chapterTarget = TimeSpan.FromSeconds(_targetTimes[chapter]);
                timer.SetTimer(UnityUtils.ConvertTimespanToTotalHoursMinutesSeconds(chapterTarget)); // always use same format so they look even
            }
            else
            {
                timer.SetTimer(_speedrunSettings.GetDefaultTimerText());
            }

            SpeedrunSettings.SpeedrunTimerConfig config = _speedrunSettings.GetPlayerSelectedTimerConfig();
            if (config.showPersonalBestDeltas)
            {
                float chapterTime = GetIndividualSplit(chapter);
                bool timeDataExists = !chapterTime.Approx(0f);
                if (targetTimeExists && timeDataExists)
                {
                    // If we're loading a saved game the time data already exists
                    float chapterSplitSeconds = GetIndividualSplit(chapter);
                    _deltasAreCumulative = config.cumulativePersonalBestDeltas;
                    AddDeltaTimeToSegmentTimer(chapterSplitSeconds, chapter, _deltasAreCumulative);
                }
                else
                {
                    // we want the timer to exist in the UI and reserve space for its info, but be invisible
                    // until the player actually finishes that chapter
                    timer.SetDiffTimer("");
                }
            }
            else
            {
                timer.HideDiffTimer();
            }
        }

        protected override void AddDeltaTimeToSegmentTimer(float segmentTime, int segmentIndex, bool cumulativeDeltas = false)
        {
            base.AddDeltaTimeToSegmentTimer(segmentTime, segmentIndex, cumulativeDeltas);
            SpeedrunSettings.PBStyleSettings pbsettings = _speedrunSettings.GetPersonalBestsStyleSettings();
            Color c = pbsettings.passedTarget;
            GTWGameWorldDefinition currentWorldDefinition = _doinklerPortfolioDefinition.GTWGameWorldDefinitions[segmentIndex];
            if(!currentWorldDefinition.IsWorldCompleted)
            {
                _segmentTimes[segmentIndex].SetDiffTimer("Incomplete"); // TODO: JP - Add localization here
                _segmentTimes[segmentIndex].SetDiffColor(c);
            }
        }

        protected override float GetIndividualSplit(int progressLevel)
        {
            if(progressLevel >= _doinklerPortfolioDefinition.GTWGameWorldDefinitions.Count)
            {
                // TODO - JP - GTW-747: This occurs when we are in Unity editor and starting from the doinkler level itself and not going through the titlescene flow.
                Debug.LogError($"DoinklerSpeedrunTimer: Attempting to get time for world {progressLevel} but only have {_doinklerPortfolioDefinition.GTWGameWorldDefinitions.Count} worlds.");
                return 0f;
            }
            float totalTime = 0f;
            GTWGameWorldDefinition currentWorldDefinition = _doinklerPortfolioDefinition.GTWGameWorldDefinitions[progressLevel];
            foreach (GTWGameLevelDefinition gtwGameLevelDefinition in currentWorldDefinition.GameLevels)
            {
                totalTime += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
            }

            if (!_isTimeOverride)
            {
                totalTime += _gameProgress.GetGameSecondsElapsedInLevel(progressLevel);
            }

            return totalTime;
        }

        protected override void SaveRecordTimes()
        {
            SpeedrunTimerData data = new SpeedrunTimerData();
            int maxLevel = _gameProgress.GetMaxGameProgressLevel();
            data.times = new float[maxLevel];
            for (int i = 0; i < maxLevel; i++)
            {
                GTWGameWorldDefinition currentWorldDefinition = _doinklerPortfolioDefinition.GTWGameWorldDefinitions[i];
                if (!currentWorldDefinition.IsWorldCompleted)
                {
                    continue;
                }
                float timeTotal = 0f;
                foreach (GTWGameLevelDefinition gtwGameLevelDefinition in currentWorldDefinition.GameLevels)
                {
                    timeTotal += PlayerPrefs.GetFloat(gtwGameLevelDefinition.UniqueIDTotal);
                }
                data.times[i] = timeTotal;
            }

            switch (_speedrunSettings.GetPersonalBestSaveLocation())
            {
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.DoNotSave:
                    Debug.LogWarning($"Best splits currently configured not to be saved.");
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.SaveSlot:
                    SaveRecordToSlot(data);
                    break;
                case SpeedrunSettings.SpeedrunPersonalBestsSaveLocationEnum.LocalLow:
                    SaveRecordToLocalLow(data);
                    break;
                default:
                    Debug.LogError($"Best splits save location has unsupported configuration {_speedrunSettings.GetPersonalBestSaveLocation()}");
                    break;
            }
        }
    }
}